import os
import re

def load_game_list(file_path):
    """加载游戏列表文件，返回appid到游戏名的映射"""
    game_dict = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '----' in line:
                    parts = line.split('----')
                    if len(parts) == 2:
                        appid = parts[0].strip()
                        game_name = parts[1].strip()
                        game_dict[game_name] = appid
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return {}
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return {}
    
    return game_dict

def scan_folders(target_path, game_dict):
    """扫描目标文件夹，返回找到的游戏和未找到的文件夹"""
    found_games = []
    not_found_folders = []
    
    if not os.path.exists(target_path):
        print(f"错误：目标路径不存在 {target_path}")
        return found_games, not_found_folders
    
    try:
        for folder_name in os.listdir(target_path):
            folder_path = os.path.join(target_path, folder_name)
            if os.path.isdir(folder_path):
                # 尝试精确匹配
                if folder_name in game_dict:
                    found_games.append((folder_name, game_dict[folder_name]))
                else:
                    # 尝试模糊匹配（去除特殊字符和空格）
                    normalized_folder = re.sub(r'[^\w\u4e00-\u9fff]', '', folder_name.lower())
                    found_match = False
                    
                    for game_name, appid in game_dict.items():
                        normalized_game = re.sub(r'[^\w\u4e00-\u9fff]', '', game_name.lower())
                        if normalized_folder in normalized_game or normalized_game in normalized_folder:
                            found_games.append((folder_name, appid))
                            found_match = True
                            break
                    
                    if not found_match:
                        not_found_folders.append(folder_name)
    
    except Exception as e:
        print(f"扫描文件夹时出错：{e}")
    
    return found_games, not_found_folders

def save_results(found_games, output_file):
    """保存结果到txt文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for folder_name, appid in found_games:
                f.write(f"{appid}\n")
        print(f"结果已保存到 {output_file}")
    except Exception as e:
        print(f"保存文件时出错：{e}")

def main():
    # 配置文件路径
    game_list_file = "单机游戏列表.txt"
    target_folder = r"d:\Downloads\单机游戏\单机游戏"
    output_file = "游戏appid结果.txt"
    
    print("开始加载游戏列表...")
    game_dict = load_game_list(game_list_file)
    print(f"成功加载 {len(game_dict)} 个游戏信息")
    
    print(f"\n开始扫描文件夹：{target_folder}")
    found_games, not_found_folders = scan_folders(target_folder, game_dict)
    
    print(f"\n扫描完成！")
    print(f"找到匹配的游戏：{len(found_games)} 个")
    print(f"未找到匹配的文件夹：{len(not_found_folders)} 个")
    
    # 保存结果
    if found_games:
        save_results(found_games, output_file)
    
    # 打印未找到的文件夹
    if not_found_folders:
        print(f"\n未找到匹配的文件夹：")
        for folder in not_found_folders:
            print(f"  - {folder}")
    
    # 打印找到的游戏
    if found_games:
        print(f"\n找到的游戏：")
        for folder_name, appid in found_games:
            print(f"  - {folder_name} -> {appid}")

if __name__ == "__main__":
    main()
